# cPanel Production Environment Configuration
# Copy this file to .env.production and fill in your actual values

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# CHOOSE YOUR DEPLOYMENT MODE:

# Option 1: Static Export (recommended for basic cPanel hosting)
# Set to 'true' for static file deployment (no Node.js required)
CPANEL_DEPLOY=true

# Option 2: Node.js Server (for advanced cPanel hosting with Node.js support)
# Set to 'true' to use custom Node.js server (requires Node.js hosting)
# CPANEL_USE_SERVER=true
# PORT=3000

# Your domain name (replace with your actual domain)
NEXT_PUBLIC_APP_URL=https://yourdomain.com

# Base path if deploying to a subdirectory (leave empty for root domain)
# Example: /myapp if your site is at yourdomain.com/myapp
CPANEL_BASE_PATH=

# Asset prefix for CDN or subdirectory deployment (usually same as base path)
CPANEL_ASSET_PREFIX=

# =============================================================================
# DATABASE CONFIGURATION (cPanel MySQL)
# =============================================================================

# cPanel MySQL Database Configuration
# Get these from your cPanel MySQL Databases section
DB_HOST=localhost
DB_PORT=3306
DB_NAME=your_cpanel_database_name
DB_USER=your_cpanel_database_user
DB_PASSWORD=your_cpanel_database_password

# Alternative: Full database URL format
# DATABASE_URL=mysql://username:password@localhost:3306/database_name

# =============================================================================
# AUTHENTICATION
# =============================================================================

# Generate a secure random string for AUTH_SECRET
# You can use: openssl rand -base64 32
AUTH_SECRET=your_secure_random_string_here

# GitHub OAuth (optional)
AUTH_GITHUB_ID=your_github_client_id
AUTH_GITHUB_SECRET=your_github_client_secret

# Google OAuth (optional)
AUTH_GOOGLE_ID=your_google_client_id
AUTH_GOOGLE_SECRET=your_google_client_secret

# =============================================================================
# EXTERNAL API SERVICES
# =============================================================================

# Unsplash API for stock images
NEXT_PUBLIC_UNSPLASH_ACCESS_KEY=your_unsplash_access_key

# Together AI for AI image generation
TOGETHER_API_KEY=your_together_ai_api_key
NEXT_PUBLIC_TOGETHER_API_KEY=your_together_ai_api_key

# Replicate API for AI services
REPLICATE_API_TOKEN=your_replicate_api_token

# ClipDrop API for background removal
CLIPDROP_API_KEY=your_clipdrop_api_key
NEXT_PUBLIC_CLIPDROP_API_KEY=your_clipdrop_api_key

# FAL AI API
FAL_KEY=your_fal_ai_api_key

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================

# UploadThing configuration (if using)
UPLOADTHING_SECRET=your_uploadthing_secret
UPLOADTHING_APP_ID=your_uploadthing_app_id

# =============================================================================
# PAYMENT PROCESSING (STRIPE)
# =============================================================================

# Stripe configuration (if using payments)
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_PRICE_ID=your_stripe_price_id
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# =============================================================================
# EMAIL CONFIGURATION (if needed)
# =============================================================================

# SMTP settings for cPanel email
SMTP_HOST=mail.yourdomain.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_FROM=<EMAIL>

# =============================================================================
# SECURITY & PERFORMANCE
# =============================================================================

# Enable security headers
SECURITY_HEADERS=true

# Enable compression
ENABLE_COMPRESSION=true

# Cache duration (in seconds)
CACHE_DURATION=3600

# =============================================================================
# LOGGING & MONITORING
# =============================================================================

# Log level (error, warn, info, debug)
LOG_LEVEL=error

# Enable error reporting (set to false in production for security)
ENABLE_ERROR_REPORTING=false
