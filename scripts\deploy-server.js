#!/usr/bin/env node

/**
 * cPanel Node.js Server Deployment Script
 * Use this when your cPanel hosting supports Node.js and you want server-side features
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Starting cPanel Node.js server deployment...\n');

// Check if .env.production exists
if (!fs.existsSync('.env.production')) {
  console.error('❌ .env.production not found!');
  console.log('Please run "npm run cpanel:setup" first or create .env.production manually.');
  process.exit(1);
}

// Load environment variables
require('dotenv').config({ path: '.env.production' });

// Check if server.js exists
if (!fs.existsSync('server.js')) {
  console.error('❌ server.js not found!');
  console.log('The server.js file is required for Node.js deployment.');
  process.exit(1);
}

console.log('✅ Server deployment mode detected');

// Validate required environment variables
const requiredEnvVars = [
  'NEXT_PUBLIC_APP_URL'
];

console.log('🔍 Validating environment variables...');
let missingVars = [];

requiredEnvVars.forEach(varName => {
  if (!process.env[varName]) {
    missingVars.push(varName);
  }
});

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingVars.forEach(varName => console.log(`   - ${varName}`));
  console.log('\nPlease update your .env.production file.');
  process.exit(1);
}

console.log('✅ Environment variables validated');

// Clean previous build
console.log('\n🧹 Cleaning previous build...');
if (fs.existsSync('.next')) {
  fs.rmSync('.next', { recursive: true, force: true });
  console.log('✅ Cleaned .next directory');
}

// Build the project for server mode
console.log('\n🔨 Building project for Node.js server...');
try {
  execSync('npm run build:server', { 
    stdio: 'inherit',
    env: { ...process.env, NODE_ENV: 'production', CPANEL_USE_SERVER: 'true' }
  });
  console.log('✅ Build completed successfully');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

// Create deployment directory
const deployDir = 'deploy-server';
console.log(`\n📁 Creating deployment directory: ${deployDir}`);

if (fs.existsSync(deployDir)) {
  fs.rmSync(deployDir, { recursive: true, force: true });
}
fs.mkdirSync(deployDir, { recursive: true });

// Copy necessary files for server deployment
console.log('\n📋 Copying files for server deployment...');

const filesToCopy = [
  'server.js',
  'package.json',
  'package-lock.json',
  '.env.production',
  'next.config.mjs'
];

const directoriesToCopy = [
  '.next',
  'public'
];

// Copy files
filesToCopy.forEach(file => {
  if (fs.existsSync(file)) {
    fs.copyFileSync(file, path.join(deployDir, file));
    console.log(`✅ Copied ${file}`);
  } else {
    console.log(`⚠️  ${file} not found, skipping`);
  }
});

// Copy directories
directoriesToCopy.forEach(dir => {
  if (fs.existsSync(dir)) {
    const destDir = path.join(deployDir, dir);
    fs.mkdirSync(destDir, { recursive: true });
    
    // Copy directory contents recursively
    const copyRecursive = (src, dest) => {
      const items = fs.readdirSync(src);
      items.forEach(item => {
        const srcPath = path.join(src, item);
        const destPath = path.join(dest, item);
        
        if (fs.statSync(srcPath).isDirectory()) {
          fs.mkdirSync(destPath, { recursive: true });
          copyRecursive(srcPath, destPath);
        } else {
          fs.copyFileSync(srcPath, destPath);
        }
      });
    };
    
    copyRecursive(dir, destDir);
    console.log(`✅ Copied ${dir} directory`);
  }
});

// Create a production package.json (only production dependencies)
console.log('\n📦 Creating production package.json...');
const originalPackageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const productionPackageJson = {
  name: originalPackageJson.name,
  version: originalPackageJson.version,
  private: originalPackageJson.private,
  scripts: {
    start: "node server.js",
    "start:production": "NODE_ENV=production node server.js"
  },
  dependencies: originalPackageJson.dependencies,
  engines: {
    node: ">=18.0.0",
    npm: ">=8.0.0"
  }
};

fs.writeFileSync(
  path.join(deployDir, 'package.json'), 
  JSON.stringify(productionPackageJson, null, 2)
);
console.log('✅ Created production package.json');

// Create startup script for cPanel
console.log('\n🚀 Creating cPanel startup script...');
const startupScript = `#!/bin/bash

# cPanel Node.js Startup Script
# Place this in your cPanel Node.js app startup file

echo "🚀 Starting Canva Clone Node.js server..."

# Set environment
export NODE_ENV=production

# Install dependencies (if needed)
if [ ! -d "node_modules" ]; then
  echo "📦 Installing dependencies..."
  npm ci --only=production
fi

# Start the server
echo "✅ Starting server..."
node server.js
`;

fs.writeFileSync(path.join(deployDir, 'startup.sh'), startupScript);
fs.chmodSync(path.join(deployDir, 'startup.sh'), '755');
console.log('✅ Created startup.sh script');

// Create .htaccess for Node.js app (different from static)
console.log('\n🌐 Creating Node.js .htaccess...');
const nodeHtaccess = `# Node.js Application .htaccess
# This redirects all requests to your Node.js app

RewriteEngine On

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Redirect all requests to Node.js app (adjust port as needed)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ http://localhost:3000/$1 [P,L]

# Or if using subdomain/port setup:
# RewriteRule ^(.*)$ http://your-app-url.com:3000/$1 [P,L]
`;

fs.writeFileSync(path.join(deployDir, '.htaccess'), nodeHtaccess);
console.log('✅ Created Node.js .htaccess');

// Create deployment info
const deploymentInfo = {
  timestamp: new Date().toISOString(),
  version: originalPackageJson.version,
  nodeVersion: process.version,
  environment: 'production',
  deploymentType: 'cpanel-nodejs-server',
  serverFile: 'server.js',
  port: process.env.PORT || 3000
};

fs.writeFileSync(
  path.join(deployDir, 'deployment-info.json'), 
  JSON.stringify(deploymentInfo, null, 2)
);
console.log('✅ Created deployment info');

// Create README for server deployment
const serverReadme = `# cPanel Node.js Server Deployment

## Files in this directory:
- server.js - Custom Node.js server
- package.json - Production dependencies only
- .next/ - Built Next.js application
- public/ - Static assets
- startup.sh - cPanel startup script
- .htaccess - Apache configuration for Node.js

## Deployment Steps:

1. **Upload all files** to your cPanel Node.js app directory
2. **Set Node.js version** to 18+ in cPanel
3. **Set startup file** to server.js in cPanel
4. **Install dependencies**: npm ci --only=production
5. **Start the application** in cPanel Node.js interface

## Environment Variables:
Set these in cPanel Node.js environment:
- NODE_ENV=production
- PORT=3000 (or your assigned port)
- NEXT_PUBLIC_APP_URL=https://yourdomain.com
- DATABASE_URL=your_database_connection_string
- AUTH_SECRET=your_auth_secret
- (other API keys as needed)

## Troubleshooting:
- Check cPanel Node.js logs for errors
- Ensure all environment variables are set
- Verify database connection
- Check file permissions
`;

fs.writeFileSync(path.join(deployDir, 'README.md'), serverReadme);
console.log('✅ Created deployment README');

console.log('\n🎉 Server deployment preparation completed!');
console.log(`\n📁 Files ready for upload in the "${deployDir}" directory`);
console.log('\n📝 Next steps for Node.js deployment:');
console.log('1. Upload all files from deploy-server/ to your cPanel Node.js app directory');
console.log('2. Set Node.js version to 18+ in cPanel');
console.log('3. Set startup file to "server.js" in cPanel Node.js settings');
console.log('4. Configure environment variables in cPanel');
console.log('5. Install dependencies: npm ci --only=production');
console.log('6. Start the application in cPanel Node.js interface');
console.log('\n📚 See deploy-server/README.md for detailed instructions.');
