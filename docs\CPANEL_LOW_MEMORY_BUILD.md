# cPanel Low Memory Build Guide

This guide helps you build and deploy your Canva Clone application on cPanel hosting with limited memory resources.

## Quick Start

For immediate low memory build:

```bash
npm run build:cpanel-low-memory
```

This command automatically:
- Clears all caches to free memory
- Sets memory-optimized environment variables
- Builds with reduced memory usage settings

## Memory Optimizations Included

### 1. Build Process Optimizations
- **Single-threaded compilation**: Reduces concurrent memory usage
- **Aggressive chunk splitting**: Smaller chunks require less memory
- **Disabled source maps**: Saves significant memory during build
- **Reduced optimization passes**: Faster build with less memory usage

### 2. Webpack Optimizations
- **Limited parallel processing**: Uses single thread for minification
- **Smaller chunk sizes**: Maximum 200KB per chunk
- **Optimized module resolution**: Disabled symlinks and context caching
- **Memory-efficient minification**: Reduced Terser optimization passes

### 3. Next.js Optimizations
- **Disabled worker threads**: Prevents memory fragmentation
- **Loose ESM externals**: Reduces bundling overhead
- **Disabled memory-based workers**: Uses file-based compilation

## Manual Memory Optimization

If you need more control, run optimizations manually:

```bash
# 1. Clear all caches
node scripts/memory-optimize.js

# 2. Set environment variables
export NODE_OPTIONS="--max-old-space-size=1024 --optimize-for-size"
export CPANEL_LOW_MEMORY=true
export NEXT_TELEMETRY_DISABLED=1

# 3. Build with low memory settings
npm run build:cpanel
```

## System Requirements

### Minimum Requirements
- **RAM**: 512MB available memory
- **Storage**: 2GB free space
- **Node.js**: Version 18+ with memory optimization flags

### Recommended Requirements
- **RAM**: 1GB+ available memory
- **Storage**: 4GB+ free space
- **Swap**: 1GB swap space (if available)

## Troubleshooting Memory Issues

### Build Fails with "Out of Memory"

1. **Increase Node.js memory limit**:
   ```bash
   export NODE_OPTIONS="--max-old-space-size=2048"
   ```

2. **Clear all caches manually**:
   ```bash
   rm -rf .next node_modules/.cache tsconfig.tsbuildinfo
   ```

3. **Close other applications** during build

4. **Use swap space** if available on your system

### Build is Very Slow

1. **Check available memory**:
   ```bash
   free -h  # Linux
   # or check Task Manager on Windows
   ```

2. **Monitor build process**:
   ```bash
   htop  # Linux
   # or Task Manager on Windows
   ```

3. **Consider building locally** and uploading the `out` folder

### Deployment Fails

1. **Check output size**:
   ```bash
   du -sh out/  # Should be under 100MB for most cPanel hosts
   ```

2. **Compress before upload**:
   ```bash
   tar -czf build.tar.gz out/
   ```

3. **Upload in smaller batches** if file manager has limits

## Environment Variables

Set these in your build environment:

```bash
# Required for low memory mode
CPANEL_LOW_MEMORY=true

# Node.js memory optimization
NODE_OPTIONS="--max-old-space-size=1024 --optimize-for-size"

# Next.js optimizations
NEXT_TELEMETRY_DISABLED=1
NODE_ENV=production
CPANEL_DEPLOY=true

# Optional: Custom paths
CPANEL_BASE_PATH=/your-subdirectory
CPANEL_ASSET_PREFIX=https://your-cdn.com
```

## Build Scripts Reference

| Script | Purpose | Memory Usage |
|--------|---------|--------------|
| `build:cpanel` | Standard cPanel build | Normal |
| `build:cpanel-low-memory` | Optimized for low memory | Minimal |
| `memory-optimize` | Pre-build optimization | N/A |

## Performance Tips

### Before Building
1. Close unnecessary applications
2. Clear browser caches
3. Restart your development environment
4. Check available disk space

### During Building
1. Don't run other memory-intensive tasks
2. Monitor memory usage
3. Be patient - low memory builds are slower
4. Keep terminal/command prompt open

### After Building
1. Verify build output size
2. Test the static export locally
3. Compress files before upload
4. Upload during off-peak hours

## Common Issues and Solutions

### Issue: "JavaScript heap out of memory"
**Solution**: Increase Node.js memory limit or use low memory build

### Issue: Build takes too long
**Solution**: Use local build and upload, or upgrade hosting

### Issue: Large bundle size
**Solution**: Check for large assets in public folder

### Issue: Upload fails
**Solution**: Compress and upload in smaller batches

## Support

If you continue to experience memory issues:

1. Check your cPanel hosting specifications
2. Consider upgrading to a plan with more memory
3. Use local build and upload strategy
4. Contact your hosting provider for memory limits

For technical issues, check the build logs and error messages for specific guidance.
