# 🚀 cPanel Quick Start Guide

Deploy your Canva Clone to cPanel hosting in 5 simple steps!

## 🎯 Choose Your Deployment Mode

### Option 1: 📄 Static Export (Recommended)
- ✅ Works on ALL cPanel hosting
- ✅ No Node.js required
- ✅ Faster & cheaper

### Option 2: 🖥️ Node.js Server
- ✅ Full server features
- ❌ Requires Node.js hosting

## ⚡ Quick Deploy (5 minutes)

### 1. Setup & Choose Mode
```bash
npm run cpanel:setup
npm run cpanel:mode  # Choose deployment mode
```

### 2. Configure
Edit `.env.production` with your domain and API keys:
```bash
NEXT_PUBLIC_APP_URL=https://yourdomain.com
CPANEL_DEPLOY=true
# Add your database and API credentials
```

### 3. Check
```bash
npm run cpanel:check
```

### 4. Build & Deploy

**For Static Export:**
```bash
npm run deploy:cpanel
# Upload 'out' directory to public_html
```

**For Node.js Server:**
```bash
npm run deploy:server
# Upload 'deploy-server' directory to cPanel
# Configure Node.js app in cPanel
```

## 🗄️ Database Setup

1. **Create MySQL database** in cPanel
2. **Update `.env.production`** with database credentials
3. **Import your schema** (if needed)

## ✅ Verification

- [ ] Website loads at your domain
- [ ] All routes work (no 404s)
- [ ] Images display correctly
- [ ] API functions work
- [ ] Database connections successful

## 🆘 Need Help?

- **Issues?** Run `npm run cpanel:check`
- **Detailed guide:** See `CPANEL_DEPLOYMENT.md`
- **Troubleshooting:** Check browser console and cPanel error logs

## 📋 Common Commands

```bash
# Full deployment process
npm run cpanel:setup
npm run cpanel:check
npm run deploy:cpanel

# Development
npm run dev

# Testing build
npm run build:cpanel
```

---

**🎉 That's it!** Your Canva Clone should now be running on cPanel hosting.
