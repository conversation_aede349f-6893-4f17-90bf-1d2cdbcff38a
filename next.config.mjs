/** @type {import('next').NextConfig} */
const nextConfig = {
  // cPanel deployment configuration
  // Static export for basic cPanel hosting, undefined for Node.js server hosting
  output: process.env.NODE_ENV === 'production' && process.env.CPANEL_DEPLOY === 'true' ? 'export' : undefined,

  // Base path for subdirectory deployment (if needed)
  basePath: process.env.CPANEL_BASE_PATH || '',

  // Asset prefix for CDN or subdirectory deployment
  assetPrefix: process.env.CPANEL_ASSET_PREFIX || '',

  // Low memory build optimizations for cPanel
  ...(process.env.CPANEL_LOW_MEMORY === 'true' && {
    // Webpack optimizations for low memory
    webpack: (config, { dev }) => {
      if (!dev && process.env.CPANEL_LOW_MEMORY === 'true') {
        // Reduce memory usage
        config.optimization = {
          ...config.optimization,
          // Split chunks more aggressively to reduce memory usage
          splitChunks: {
            chunks: 'all',
            minSize: 10000,
            maxSize: 200000,
            cacheGroups: {
              vendor: {
                test: /[\\/]node_modules[\\/]/,
                name: 'vendors',
                chunks: 'all',
                maxSize: 200000,
              },
              common: {
                name: 'common',
                minChunks: 2,
                chunks: 'all',
                maxSize: 200000,
              },
            },
          },
          // Reduce memory usage during minification
          minimize: true,
          minimizer: config.optimization.minimizer?.map(minimizer => {
            if (minimizer.constructor.name === 'TerserPlugin') {
              minimizer.options.parallel = 1; // Use single thread
              minimizer.options.terserOptions = {
                ...minimizer.options.terserOptions,
                compress: {
                  ...minimizer.options.terserOptions?.compress,
                  passes: 1, // Reduce optimization passes
                },
              };
            }
            return minimizer;
          }),
        };

        // Reduce memory usage for module resolution
        config.resolve.symlinks = false;
        config.resolve.cacheWithContext = false;

        // Disable source maps in low memory mode
        config.devtool = false;
      }

      return config;
    },
  }),

  // Image optimization: disabled for static export, enabled for server mode
  images: process.env.NODE_ENV === 'production' && process.env.CPANEL_DEPLOY === 'true' ? {
    unoptimized: true  // Required for static export
  } : {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.unsplash.com",
      },
      {
        protocol: "https",
        hostname: "utfs.io",
      },
      {
        protocol: "https",
        hostname: "replicate.delivery",
      },
      {
        protocol: "https",
        hostname: "canva-clone-ali.vercel.app",
      },
      {
        protocol: "https",
        hostname: "api.together.ai",
      },
      {
        protocol: "https",
        hostname: "fal.media",
      },
      {
        protocol: "http",
        hostname: "localhost",
        port: "3000",
      },
      {
        protocol: "http",
        hostname: "localhost",
        port: "3001",
      },
      {
        protocol: "http",
        hostname: "localhost",
        port: "3002",
      },
      {
        protocol: "http",
        hostname: "localhost",
        port: "3003",
      },
      {
        protocol: "https",
        hostname: "clipdrop-api.co",
      },
      {
        protocol: "https",
        hostname: "*.blob.core.windows.net",
      },
      {
        protocol: "https",
        hostname: "*.amazonaws.com",
      },
    ],
    // Image optimization
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60 * 60 * 24 * 7, // 1 week
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // Turbopack-optimized configuration
  experimental: {
    // Core optimizations
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', 'fabric', 'lodash.debounce'],

    // Low memory optimizations for cPanel builds
    ...(process.env.CPANEL_LOW_MEMORY === 'true' && {
      // Disable memory-intensive features during build
      esmExternals: 'loose',
      // Reduce concurrent compilation
      workerThreads: false,
      // Optimize memory usage
      memoryBasedWorkers: false,
    }),

    // Server-side packages that should NEVER be bundled for client OR edge runtime
    serverComponentsExternalPackages: [
      '@xenova/transformers',
      'postgres',
      'pg',
      'mysql2',
      'sqlite3',
      'sharp',
      'onnxruntime-node',
      'drizzle-orm',
      'drizzle-orm/postgres-js',
      'bcryptjs'
    ],

    // Turbopack-specific configuration
    turbo: {
      // Resolve extensions for better module resolution
      resolveExtensions: [
        '.js',
        '.jsx',
        '.ts',
        '.tsx',
        '.json'
      ],

      // Rules for specific file types
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },

      // Module resolution strategy
      moduleIdStrategy: 'deterministic',

      // Font loading configuration for Turbopack
      loaders: {
        '.woff': ['file-loader'],
        '.woff2': ['file-loader'],
        '.ttf': ['file-loader'],
        '.eot': ['file-loader'],
      },

      // Turbopack doesn't support resolveAlias with false values
      // Instead, rely on serverComponentsExternalPackages
    },
  },

  // Font optimization
  optimizeFonts: true,

  // Turbopack-only optimizations (no webpack)
  swcMinify: process.env.NODE_ENV === 'production',

  typescript: {
    ignoreBuildErrors: true,
  },
};

export default nextConfig;
