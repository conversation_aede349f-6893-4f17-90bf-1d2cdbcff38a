#!/usr/bin/env node

/**
 * cPanel Deployment Mode Selection Script
 * Helps users choose between static export and Node.js server deployment
 */

const fs = require('fs');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🚀 cPanel Deployment Mode Selection\n');
console.log('Choose your deployment mode based on your cPanel hosting capabilities:\n');

console.log('📋 Available Options:\n');
console.log('1. 📄 Static Export (Recommended)');
console.log('   ✅ Works on all cPanel hosting');
console.log('   ✅ No Node.js required');
console.log('   ✅ Faster loading');
console.log('   ✅ Lower cost');
console.log('   ❌ No server-side features');
console.log('   ❌ No real-time database');
console.log('   ❌ Limited API functionality\n');

console.log('2. 🖥️  Node.js Server');
console.log('   ✅ Full server-side features');
console.log('   ✅ Real-time database');
console.log('   ✅ Complete API functionality');
console.log('   ✅ Server-side rendering');
console.log('   ❌ Requires Node.js hosting');
console.log('   ❌ Higher cost');
console.log('   ❌ More complex setup\n');

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function selectMode() {
  try {
    const choice = await askQuestion('Enter your choice (1 for Static Export, 2 for Node.js Server): ');
    
    if (choice === '1') {
      console.log('\n✅ Static Export mode selected');
      await configureStaticMode();
    } else if (choice === '2') {
      console.log('\n✅ Node.js Server mode selected');
      await configureServerMode();
    } else {
      console.log('❌ Invalid choice. Please enter 1 or 2.');
      await selectMode();
    }
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    rl.close();
  }
}

async function configureStaticMode() {
  console.log('\n🔧 Configuring for Static Export deployment...');
  
  // Update .env.production
  if (fs.existsSync('.env.production')) {
    let envContent = fs.readFileSync('.env.production', 'utf8');
    
    // Set static export mode
    envContent = envContent.replace(/CPANEL_DEPLOY=.*/g, 'CPANEL_DEPLOY=true');
    envContent = envContent.replace(/CPANEL_USE_SERVER=.*/g, '# CPANEL_USE_SERVER=false');
    
    fs.writeFileSync('.env.production', envContent);
    console.log('✅ Updated .env.production for static export');
  } else {
    console.log('⚠️  .env.production not found. Run "npm run cpanel:setup" first.');
  }
  
  console.log('\n📝 Next steps for Static Export:');
  console.log('1. Run: npm run cpanel:check');
  console.log('2. Run: npm run deploy:cpanel');
  console.log('3. Upload "out" directory to cPanel public_html');
  console.log('4. Your site will work without Node.js');
}

async function configureServerMode() {
  console.log('\n🔧 Configuring for Node.js Server deployment...');
  
  // Check if hosting supports Node.js
  const hasNodeJs = await askQuestion('Does your cPanel hosting support Node.js? (y/n): ');
  
  if (hasNodeJs.toLowerCase() !== 'y' && hasNodeJs.toLowerCase() !== 'yes') {
    console.log('\n❌ Node.js server mode requires Node.js hosting support.');
    console.log('💡 Consider using Static Export mode instead.');
    console.log('   Most basic cPanel hosting doesn\'t support Node.js.');
    return;
  }
  
  // Get port information
  const port = await askQuestion('What port should the server use? (default: 3000): ') || '3000';
  
  // Update .env.production
  if (fs.existsSync('.env.production')) {
    let envContent = fs.readFileSync('.env.production', 'utf8');
    
    // Set server mode
    envContent = envContent.replace(/CPANEL_DEPLOY=.*/g, '# CPANEL_DEPLOY=false');
    envContent = envContent.replace(/# CPANEL_USE_SERVER=.*/g, 'CPANEL_USE_SERVER=true');
    
    // Add or update port
    if (envContent.includes('PORT=')) {
      envContent = envContent.replace(/PORT=.*/g, `PORT=${port}`);
    } else {
      envContent += `\nPORT=${port}`;
    }
    
    fs.writeFileSync('.env.production', envContent);
    console.log('✅ Updated .env.production for Node.js server');
  } else {
    console.log('⚠️  .env.production not found. Run "npm run cpanel:setup" first.');
  }
  
  console.log('\n📝 Next steps for Node.js Server:');
  console.log('1. Run: npm run cpanel:check');
  console.log('2. Run: npm run deploy:server');
  console.log('3. Upload "deploy-server" directory to cPanel');
  console.log('4. Configure Node.js app in cPanel');
  console.log('5. Set startup file to "server.js"');
  console.log('6. Install dependencies and start the app');
}

// Start the selection process
selectMode();
