#!/usr/bin/env node

/**
 * Test Low Memory Configuration
 * Verifies that low memory build settings are properly configured
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing low memory configuration...\n');

// Test 1: Check if next.config.mjs has low memory settings
console.log('1. Checking next.config.mjs configuration...');
try {
  const configPath = path.join(process.cwd(), 'next.config.mjs');
  const configContent = fs.readFileSync(configPath, 'utf8');
  
  const hasLowMemoryCheck = configContent.includes('CPANEL_LOW_MEMORY');
  const hasWebpackConfig = configContent.includes('webpack:');
  const hasExperimentalConfig = configContent.includes('workerThreads: false');
  
  if (hasLowMemoryCheck && hasWebpackConfig && hasExperimentalConfig) {
    console.log('✅ next.config.mjs has low memory configuration');
  } else {
    console.log('❌ next.config.mjs missing low memory configuration');
    console.log(`   - Low memory check: ${hasLowMemoryCheck ? '✅' : '❌'}`);
    console.log(`   - Webpack config: ${hasWebpackConfig ? '✅' : '❌'}`);
    console.log(`   - Experimental config: ${hasExperimentalConfig ? '✅' : '❌'}`);
  }
} catch (error) {
  console.log('❌ Could not read next.config.mjs');
}

// Test 2: Check if package.json has low memory scripts
console.log('\n2. Checking package.json scripts...');
try {
  const packagePath = path.join(process.cwd(), 'package.json');
  const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  const hasLowMemoryBuild = packageContent.scripts['build:cpanel-low-memory'];
  const hasLowMemoryDeploy = packageContent.scripts['deploy:cpanel-low-memory'];
  
  if (hasLowMemoryBuild && hasLowMemoryDeploy) {
    console.log('✅ package.json has low memory scripts');
  } else {
    console.log('❌ package.json missing low memory scripts');
    console.log(`   - build:cpanel-low-memory: ${hasLowMemoryBuild ? '✅' : '❌'}`);
    console.log(`   - deploy:cpanel-low-memory: ${hasLowMemoryDeploy ? '✅' : '❌'}`);
  }
} catch (error) {
  console.log('❌ Could not read package.json');
}

// Test 3: Check if memory optimization script exists
console.log('\n3. Checking memory optimization script...');
const memoryScriptPath = path.join(process.cwd(), 'scripts', 'memory-optimize.js');
if (fs.existsSync(memoryScriptPath)) {
  console.log('✅ memory-optimize.js script exists');
  
  // Check if script has required functions
  const scriptContent = fs.readFileSync(memoryScriptPath, 'utf8');
  const hasClearCaches = scriptContent.includes('clearCaches');
  const hasMemoryEnvironment = scriptContent.includes('setMemoryEnvironment');
  const hasSystemCheck = scriptContent.includes('checkSystemMemory');
  
  if (hasClearCaches && hasMemoryEnvironment && hasSystemCheck) {
    console.log('✅ memory-optimize.js has all required functions');
  } else {
    console.log('⚠️  memory-optimize.js missing some functions');
    console.log(`   - clearCaches: ${hasClearCaches ? '✅' : '❌'}`);
    console.log(`   - setMemoryEnvironment: ${hasMemoryEnvironment ? '✅' : '❌'}`);
    console.log(`   - checkSystemMemory: ${hasSystemCheck ? '✅' : '❌'}`);
  }
} else {
  console.log('❌ memory-optimize.js script not found');
}

// Test 4: Check if documentation exists
console.log('\n4. Checking documentation...');
const docsPath = path.join(process.cwd(), 'docs', 'CPANEL_LOW_MEMORY_BUILD.md');
if (fs.existsSync(docsPath)) {
  console.log('✅ Low memory build documentation exists');
} else {
  console.log('❌ Low memory build documentation not found');
}

// Test 5: Simulate low memory environment variables
console.log('\n5. Testing environment variable detection...');
const originalEnv = process.env.CPANEL_LOW_MEMORY;
process.env.CPANEL_LOW_MEMORY = 'true';

try {
  // This would normally require the actual next.config.mjs to be loaded
  // For now, just check if the environment variable is set correctly
  if (process.env.CPANEL_LOW_MEMORY === 'true') {
    console.log('✅ Environment variable detection works');
  } else {
    console.log('❌ Environment variable detection failed');
  }
} finally {
  // Restore original environment
  if (originalEnv) {
    process.env.CPANEL_LOW_MEMORY = originalEnv;
  } else {
    delete process.env.CPANEL_LOW_MEMORY;
  }
}

console.log('\n🎯 Low memory configuration test complete!\n');

console.log('💡 To test the actual build process:');
console.log('   npm run build:cpanel-low-memory');
console.log('\n💡 To test the full deployment:');
console.log('   npm run deploy:cpanel-low-memory');
console.log('\n📚 For more information:');
console.log('   See docs/CPANEL_LOW_MEMORY_BUILD.md');
