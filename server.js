#!/usr/bin/env node

/**
 * Custom Node.js Server for cPanel Hosting
 * Use this if your cPanel hosting supports Node.js and you want server-side features
 * 
 * To use this server instead of static export:
 * 1. Set CPANEL_DEPLOY=false in .env.production
 * 2. Set CPANEL_USE_SERVER=true in .env.production
 * 3. Run: npm run build && npm run start:server
 */

const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

// Load environment variables
require('dotenv').config({ path: '.env.production' });

const dev = process.env.NODE_ENV !== 'production';
const hostname = process.env.HOSTNAME || 'localhost';
const port = process.env.PORT || 3000;

// Initialize Next.js app
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

console.log('🚀 Starting cPanel Node.js server...');

app.prepare().then(() => {
  createServer(async (req, res) => {
    try {
      // Parse the request URL
      const parsedUrl = parse(req.url, true);
      const { pathname, query } = parsedUrl;

      // Security headers
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('X-Frame-Options', 'DENY');
      res.setHeader('X-XSS-Protection', '1; mode=block');
      res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

      // CORS headers for API routes
      if (pathname.startsWith('/api/')) {
        res.setHeader('Access-Control-Allow-Origin', process.env.NEXT_PUBLIC_APP_URL || '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
        
        // Handle preflight requests
        if (req.method === 'OPTIONS') {
          res.writeHead(200);
          res.end();
          return;
        }
      }

      // Handle the request with Next.js
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('Internal Server Error');
    }
  })
  .once('error', (err) => {
    console.error('Server error:', err);
    process.exit(1);
  })
  .listen(port, () => {
    console.log(`✅ Server ready on http://${hostname}:${port}`);
    console.log(`🌐 Environment: ${process.env.NODE_ENV}`);
    console.log(`📁 App URL: ${process.env.NEXT_PUBLIC_APP_URL}`);
    
    // Log important configuration
    if (process.env.DATABASE_URL) {
      console.log('🗄️  Database: Connected');
    } else {
      console.log('⚠️  Database: Not configured');
    }
    
    if (process.env.AUTH_SECRET) {
      console.log('🔐 Authentication: Configured');
    } else {
      console.log('⚠️  Authentication: Not configured');
    }
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});
