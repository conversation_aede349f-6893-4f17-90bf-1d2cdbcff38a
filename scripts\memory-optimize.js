#!/usr/bin/env node

/**
 * Memory Optimization Script for cPanel Low Memory Builds
 * Prepares the environment for memory-constrained builds
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🧠 Optimizing for low memory build...');

// Clear all caches to free up memory
function clearCaches() {
  console.log('🗑️  Clearing caches to free memory...');
  
  // Clear Next.js cache
  const nextCacheDir = path.join(process.cwd(), '.next');
  if (fs.existsSync(nextCacheDir)) {
    try {
      if (process.platform === 'win32') {
        execSync(`rmdir /s /q "${nextCacheDir}"`, { stdio: 'inherit' });
      } else {
        execSync(`rm -rf "${nextCacheDir}"`, { stdio: 'inherit' });
      }
      console.log('✅ Next.js cache cleared');
    } catch (error) {
      console.log('⚠️  Could not clear Next.js cache');
    }
  }

  // Clear node_modules cache
  const nodeModulesCacheDir = path.join(process.cwd(), 'node_modules', '.cache');
  if (fs.existsSync(nodeModulesCacheDir)) {
    try {
      if (process.platform === 'win32') {
        execSync(`rmdir /s /q "${nodeModulesCacheDir}"`, { stdio: 'inherit' });
      } else {
        execSync(`rm -rf "${nodeModulesCacheDir}"`, { stdio: 'inherit' });
      }
      console.log('✅ Node modules cache cleared');
    } catch (error) {
      console.log('⚠️  Could not clear node_modules cache');
    }
  }

  // Clear TypeScript cache
  const tsBuildInfoFile = path.join(process.cwd(), 'tsconfig.tsbuildinfo');
  if (fs.existsSync(tsBuildInfoFile)) {
    try {
      fs.unlinkSync(tsBuildInfoFile);
      console.log('✅ TypeScript build info cleared');
    } catch (error) {
      console.log('⚠️  Could not clear TypeScript build info');
    }
  }
}

// Check and optimize large files
function optimizeLargeFiles() {
  console.log('📊 Checking for memory-intensive files...');
  
  const publicDir = path.join(process.cwd(), 'public');
  if (fs.existsSync(publicDir)) {
    const checkDirectory = (dir, relativePath = '') => {
      const files = fs.readdirSync(dir);
      let largeFiles = [];
      
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const relativeFilePath = path.join(relativePath, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isDirectory()) {
          largeFiles = largeFiles.concat(checkDirectory(filePath, relativeFilePath));
        } else if (stats.size > 500 * 1024) { // Files larger than 500KB
          largeFiles.push({
            path: relativeFilePath,
            size: (stats.size / 1024 / 1024).toFixed(2) + 'MB'
          });
        }
      });
      
      return largeFiles;
    };
    
    const largeFiles = checkDirectory(publicDir);
    
    if (largeFiles.length > 0) {
      console.log(`⚠️  Found ${largeFiles.length} large files that may impact memory:`);
      largeFiles.forEach(file => {
        console.log(`   • ${file.path} (${file.size})`);
      });
      console.log('   Consider optimizing these files before building');
    } else {
      console.log('✅ No large files found in public directory');
    }
  }
}

// Set memory-friendly environment variables
function setMemoryEnvironment() {
  console.log('⚙️  Setting memory-optimized environment...');
  
  // Set Node.js memory options for the build process
  const nodeOptions = [
    '--max-old-space-size=1024',  // Limit heap to 1GB
    '--max-semi-space-size=64',   // Limit semi-space to 64MB
    '--optimize-for-size',        // Optimize for memory usage over speed
  ].join(' ');
  
  process.env.NODE_OPTIONS = nodeOptions;
  console.log('✅ Node.js memory options set');
  
  // Set Next.js specific optimizations
  process.env.NEXT_TELEMETRY_DISABLED = '1';  // Disable telemetry
  process.env.CPANEL_LOW_MEMORY = 'true';     // Enable low memory mode
  
  console.log('✅ Next.js optimizations enabled');
}

// Check available system memory
function checkSystemMemory() {
  console.log('💾 Checking system memory...');
  
  try {
    if (process.platform === 'linux') {
      const memInfo = execSync('cat /proc/meminfo | grep MemAvailable', { encoding: 'utf8' });
      const availableKB = parseInt(memInfo.split(/\s+/)[1]);
      const availableMB = Math.round(availableKB / 1024);
      
      console.log(`📊 Available memory: ${availableMB}MB`);
      
      if (availableMB < 512) {
        console.log('⚠️  WARNING: Very low memory detected (<512MB)');
        console.log('   Consider closing other applications during build');
      } else if (availableMB < 1024) {
        console.log('⚠️  Low memory detected (<1GB)');
        console.log('   Build may be slower but should complete');
      } else {
        console.log('✅ Sufficient memory available');
      }
    } else {
      console.log('ℹ️  Memory check not available on this platform');
    }
  } catch (error) {
    console.log('ℹ️  Could not check system memory');
  }
}

// Main optimization process
function main() {
  console.log('');
  console.log('🚀 Starting memory optimization for cPanel build...');
  console.log('');
  
  clearCaches();
  console.log('');
  
  optimizeLargeFiles();
  console.log('');
  
  setMemoryEnvironment();
  console.log('');
  
  checkSystemMemory();
  console.log('');
  
  console.log('🎯 Memory optimization complete!');
  console.log('');
  console.log('💡 Low memory build tips:');
  console.log('   • Build will use single-threaded compilation');
  console.log('   • Chunks will be split more aggressively');
  console.log('   • Source maps are disabled to save memory');
  console.log('   • Close other applications during build');
  console.log('   • Monitor memory usage: htop or Task Manager');
  console.log('');
  console.log('🚀 Ready for low memory build!');
  console.log('   Run: npm run build:cpanel-low-memory');
  console.log('');
}

// Run the optimization
main();
